"""Email connection management for IMAP and SMTP."""

import imaplib
import smtplib
import email
import logging
import os
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON><PERSON>
from email.header import decode_header
import ssl

from config.settings import settings
from .attachment_classifier import AttachmentClassifier

logger = logging.getLogger(__name__)


class EmailConnection:
    """Manages IMAP and SMTP connections for email processing."""
    
    def __init__(self):
        """Initialize email connection with settings."""
        self.imap_connection = None
        self.smtp_connection = None
        self.settings = settings

        # Create tmp directory for attachments
        self.tmp_dir = os.path.join(os.getcwd(), "tmp", "email_attachments")
        os.makedirs(self.tmp_dir, exist_ok=True)

        # Initialize attachment classifier
        self.attachment_classifier = AttachmentClassifier()
    
    def connect_imap(self) -> bool:
        """Connect to IMAP server.
        
        Returns:
            True if connection successful
        """
        try:
            if self.settings.email_use_ssl:
                self.imap_connection = imaplib.IMAP4_SSL(
                    self.settings.email_imap_server,
                    self.settings.email_imap_port
                )
            else:
                self.imap_connection = imaplib.IMAP4(
                    self.settings.email_imap_server,
                    self.settings.email_imap_port
                )
            
            # Login
            self.imap_connection.login(
                self.settings.email_username,
                self.settings.email_password
            )
            
            logger.info("IMAP connection established successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to IMAP server: {e}")
            self.imap_connection = None
            return False
    
    def connect_smtp(self) -> bool:
        """Connect to SMTP server.
        
        Returns:
            True if connection successful
        """
        try:
            self.smtp_connection = smtplib.SMTP(
                self.settings.email_smtp_server,
                self.settings.email_smtp_port
            )
            
            if self.settings.email_use_tls:
                self.smtp_connection.starttls()
            
            # Login
            self.smtp_connection.login(
                self.settings.email_username,
                self.settings.email_password
            )
            
            logger.info("SMTP connection established successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to SMTP server: {e}")
            self.smtp_connection = None
            return False
    
    def disconnect(self):
        """Disconnect from both IMAP and SMTP servers."""
        try:
            if self.imap_connection:
                self.imap_connection.logout()
                self.imap_connection = None
                logger.info("IMAP connection closed")
        except Exception as e:
            logger.error(f"Error closing IMAP connection: {e}")
        
        try:
            if self.smtp_connection:
                self.smtp_connection.quit()
                self.smtp_connection = None
                logger.info("SMTP connection closed")
        except Exception as e:
            logger.error(f"Error closing SMTP connection: {e}")
    
    def get_new_emails(self, since_timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get new emails from the inbox.
        
        Args:
            since_timestamp: ISO timestamp to get emails since (optional)
            
        Returns:
            List of email dictionaries
        """
        if not self.imap_connection:
            if not self.connect_imap():
                return []
        
        try:
            # Select inbox
            self.imap_connection.select(self.settings.email_inbox_folder)
            
            # Build search criteria
            if since_timestamp:
                # Convert ISO timestamp to IMAP date format
                dt = datetime.fromisoformat(since_timestamp.replace('Z', '+00:00'))
                date_str = dt.strftime("%d-%b-%Y")
                # Search for all emails since the date (both read and unread)
                # We'll use database tracking to avoid processing duplicates
                search_criteria = f'SINCE "{date_str}"'
            else:
                # If no timestamp, look for unread emails only (first run)
                search_criteria = "UNSEEN"
            
            # Search for emails
            logger.info(f"Searching emails with criteria: {search_criteria}")
            status, message_ids = self.imap_connection.search(None, search_criteria)

            if status != 'OK':
                logger.error(f"Failed to search emails: {status}")
                return []

            emails = []
            message_ids = message_ids[0].split()
            logger.info(f"Found {len(message_ids)} emails matching search criteria")

            for msg_id in message_ids:
                try:
                    email_data = self._fetch_email(msg_id.decode())
                    if email_data:
                        emails.append(email_data)
                except Exception as e:
                    logger.error(f"Failed to fetch email {msg_id}: {e}")
                    continue
            
            logger.info(f"Retrieved {len(emails)} new emails")
            return emails
            
        except Exception as e:
            logger.error(f"Failed to get new emails: {e}")
            return []
    
    def _fetch_email(self, msg_id: str) -> Optional[Dict[str, Any]]:
        """Fetch and parse a single email.
        
        Args:
            msg_id: Message ID
            
        Returns:
            Email data dictionary or None if failed
        """
        try:
            # Fetch email
            status, msg_data = self.imap_connection.fetch(msg_id, '(RFC822)')
            
            if status != 'OK':
                logger.error(f"Failed to fetch email {msg_id}")
                return None
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract basic information
            subject = self._decode_header(email_message.get('Subject', ''))
            sender = self._decode_header(email_message.get('From', ''))
            date_str = email_message.get('Date', '')
            
            # Parse date
            received_at = datetime.now(timezone.utc).isoformat()
            if date_str:
                try:
                    parsed_date = email.utils.parsedate_to_datetime(date_str)
                    received_at = parsed_date.isoformat()
                except Exception as e:
                    logger.warning(f"Failed to parse email date {date_str}: {e}")
            
            # Extract body
            body = self._extract_body(email_message)

            # Extract attachments
            attachments = self._extract_attachments(email_message, msg_id, subject, body)

            return {
                'uid': msg_id,
                'subject': subject,
                'sender': sender,
                'received_at': received_at,
                'body': body,
                'attachments': attachments,
                'raw_message': email_message
            }
            
        except Exception as e:
            logger.error(f"Failed to fetch email {msg_id}: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """Decode email header.
        
        Args:
            header: Raw header string
            
        Returns:
            Decoded header string
        """
        if not header:
            return ""
        
        try:
            decoded_parts = decode_header(header)
            decoded_header = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_header += part.decode(encoding)
                    else:
                        decoded_header += part.decode('utf-8', errors='ignore')
                else:
                    decoded_header += part
            
            return decoded_header
            
        except Exception as e:
            logger.error(f"Failed to decode header {header}: {e}")
            return header
    
    def _extract_body(self, email_message) -> str:
        """Extract email body text.
        
        Args:
            email_message: Email message object
            
        Returns:
            Email body as string
        """
        body = ""
        
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue
                    
                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        body = part.get_payload(decode=True).decode(charset, errors='ignore')
                        break
                    elif content_type == "text/html" and not body:
                        # Fallback to HTML if no plain text
                        charset = part.get_content_charset() or 'utf-8'
                        body = part.get_payload(decode=True).decode(charset, errors='ignore')
            else:
                charset = email_message.get_content_charset() or 'utf-8'
                body = email_message.get_payload(decode=True).decode(charset, errors='ignore')
            
        except Exception as e:
            logger.error(f"Failed to extract email body: {e}")
            body = "Failed to extract email content"
        
        return body.strip()

    def _extract_attachments(self, email_message, msg_id: str, email_subject: str, email_body: str) -> List[Dict[str, Any]]:
        """Extract and save email attachments.

        Args:
            email_message: Email message object
            msg_id: Message ID for unique folder naming
            email_subject: Email subject for classification context
            email_body: Email body for classification context

        Returns:
            List of attachment metadata dictionaries
        """
        attachments = []

        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_disposition = str(part.get("Content-Disposition", ""))
                    content_type = part.get_content_type()

                    # Check if this is an attachment
                    if "attachment" in content_disposition:
                        filename = part.get_filename()
                        if filename:
                            # Decode filename if needed
                            filename = self._decode_header(filename)

                            # Generate unique filename to avoid conflicts
                            unique_id = str(uuid.uuid4())[:8]
                            safe_filename = f"{unique_id}_{filename}"

                            # Create email-specific directory
                            email_dir = os.path.join(self.tmp_dir, f"email_{msg_id}")
                            os.makedirs(email_dir, exist_ok=True)

                            # Save attachment
                            file_path = os.path.join(email_dir, safe_filename)

                            try:
                                with open(file_path, 'wb') as f:
                                    f.write(part.get_payload(decode=True))

                                # Get file size
                                file_size = os.path.getsize(file_path)

                                attachment_info = {
                                    'filename': filename,
                                    'safe_filename': safe_filename,
                                    'file_path': file_path,
                                    'content_type': content_type,
                                    'file_size': file_size,
                                    'unique_id': unique_id,
                                    'email_uid': msg_id
                                }

                                attachments.append(attachment_info)
                                logger.info(f"Saved attachment: {filename} ({file_size} bytes)")

                            except Exception as e:
                                logger.error(f"Failed to save attachment {filename}: {e}")
                                continue

        except Exception as e:
            logger.error(f"Failed to extract attachments from email {msg_id}: {e}")

        # Classify attachments to filter out logos/signatures
        if attachments:
            try:
                classified_attachments = self.attachment_classifier.classify_attachments(
                    attachments, email_subject, email_body
                )
                # Only return relevant attachments
                relevant_attachments = [att for att in classified_attachments if att.get('is_relevant', True)]
                logger.info(f"Filtered {len(attachments)} attachments to {len(relevant_attachments)} relevant ones")
                return relevant_attachments
            except Exception as e:
                logger.error(f"Failed to classify attachments: {e}")
                # Return all attachments if classification fails
                return attachments

        return attachments

    def send_reply(self, original_email: Dict[str, Any], reply_content: str) -> bool:
        """Send a reply to an email.
        
        Args:
            original_email: Original email data
            reply_content: Reply content
            
        Returns:
            True if sent successfully
        """
        if not self.smtp_connection:
            if not self.connect_smtp():
                return False
        
        try:
            # Create reply message
            msg = MIMEMultipart()
            msg['From'] = self.settings.email_username
            msg['To'] = original_email['sender']
            msg['Subject'] = f"Re: {original_email['subject']}"
            
            # Add reply content
            msg.attach(MIMEText(reply_content, 'plain', 'utf-8'))
            
            # Send email
            self.smtp_connection.send_message(msg)
            
            logger.info(f"Reply sent to {original_email['sender']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send reply: {e}")
            return False
