"""Email processing logic for AI agent instructions."""

import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List

from agent import run_agent
from .connection import EmailConnection
from .database import EmailDatabase
from .whitelist import <PERSON><PERSON><PERSON><PERSON>elist
from config.settings import settings

logger = logging.getLogger(__name__)


class EmailProcessor:
    """Processes emails containing AI agent instructions."""
    
    def __init__(self):
        """Initialize email processor."""
        self.email_connection = EmailConnection()
        self.database = EmailDatabase()

        # Initialize email whitelist
        self.whitelist = EmailWhitelist(
            whitelist_string=settings.email_whitelist,
            enabled=settings.email_whitelist_enabled
        )

        # Log whitelist configuration
        whitelist_info = self.whitelist.get_whitelist_info()
        if whitelist_info["enabled"]:
            logger.info(f"Email whitelist enabled with {whitelist_info['total_entries']} entries: "
                       f"{whitelist_info['exact_emails']} exact emails, "
                       f"{whitelist_info['domain_patterns']} domain patterns")
        else:
            logger.info("Email whitelist disabled - all emails will be processed")
    
    def process_new_emails(self) -> Dict[str, Any]:
        """Process all new emails since last sync.
        
        Returns:
            Processing results summary
        """
        logger.info("Starting email processing cycle")
        
        # Get last sync timestamp
        last_sync = self.database.get_last_sync_timestamp()
        logger.info(f"Last sync timestamp: {last_sync}")

        # Connect to email server
        if not self.email_connection.connect_imap():
            return {
                "success": False,
                "error": "Failed to connect to IMAP server",
                "processed_count": 0
            }
        
        try:
            # Get new emails
            new_emails = self.email_connection.get_new_emails(last_sync)
            
            if not new_emails:
                logger.info("No new emails to process")
                return {
                    "success": True,
                    "processed_count": 0,
                    "message": "No new emails found"
                }
            
            # Process each email
            processed_count = 0
            failed_count = 0
            
            for email_data in new_emails:
                try:
                    # Check if already processed
                    if self.database.is_email_processed(email_data['uid']):
                        logger.info(f"Email {email_data['uid']} already processed, skipping")
                        continue
                    
                    # Process the email
                    success = self._process_single_email(email_data)
                    
                    if success:
                        processed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"Failed to process email {email_data.get('uid', 'unknown')}: {e}")
                    failed_count += 1
                    
                    # Mark as processed with error
                    try:
                        self.database.mark_email_processed(
                            email_data,
                            thread_id="error",
                            response_sent=False,
                            error_message=str(e)
                        )
                    except Exception as db_error:
                        logger.error(f"Failed to mark email as failed: {db_error}")
            
            # Update sync timestamp
            current_time = datetime.now().isoformat()
            last_email_uid = new_emails[-1]['uid'] if new_emails else None
            self.database.update_sync_timestamp(current_time, last_email_uid)
            
            logger.info(f"Email processing completed: {processed_count} processed, {failed_count} failed")
            
            return {
                "success": True,
                "processed_count": processed_count,
                "failed_count": failed_count,
                "total_emails": len(new_emails),
                "last_sync": current_time
            }
            
        except Exception as e:
            logger.error(f"Email processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processed_count": 0
            }
        
        finally:
            self.email_connection.disconnect()
    
    def _process_single_email(self, email_data: Dict[str, Any]) -> bool:
        """Process a single email with AI agent.
        
        Args:
            email_data: Email data dictionary
            
        Returns:
            True if processed successfully
        """
        try:
            logger.info(f"Processing email from {email_data['sender']}: {email_data['subject']}")

            # Check whitelist before processing
            if not self.whitelist.is_allowed(email_data['sender']):
                logger.info(f"Email from {email_data['sender']} not in whitelist - skipping processing")

                # Mark as processed but with whitelist rejection reason
                thread_id = f"email_{email_data['uid']}_{uuid.uuid4().hex[:8]}"
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Email sender not in whitelist"
                )
                return True  # Return True to indicate successful handling (even though skipped)

            # Generate unique thread ID for this email
            thread_id = f"email_{email_data['uid']}_{uuid.uuid4().hex[:8]}"

            # Use email body directly as input to the agent
            email_content = email_data.get('body', '').strip()

            if not email_content:
                logger.warning(f"Email {email_data['uid']} has no content")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Email has no content"
                )
                return False

            # Save attachments to database if any
            attachments = email_data.get('attachments', [])
            if attachments:
                self.database.save_email_attachments(email_data['uid'], attachments)
                logger.info(f"Saved {len(attachments)} attachments for email {email_data['uid']}")

            # Run the AI agent with the full email content
            agent_result = run_agent(email_content, email_data['sender'], thread_id)
            
            if not agent_result.get("success"):
                logger.error(f"Agent processing failed for email {email_data['uid']}: {agent_result.get('error')}")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message=agent_result.get('error', 'Agent processing failed')
                )
                return False
            
            # Format the response as HTML
            response_content = agent_result.get('output', 'No response generated')
            pending_emails = agent_result.get('pending_emails', [])

            # Create HTML formatted reply
            reply = self._format_html_response(response_content, pending_emails)

            # Send reply
            if not self.email_connection.connect_smtp():
                logger.error("Failed to connect to SMTP server for reply")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Failed to connect to SMTP server"
                )
                return False
            
            reply_sent = self.email_connection.send_reply(email_data, reply)
            
            if reply_sent:
                logger.info(f"Successfully processed and replied to email {email_data['uid']}")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=True
                )
                return True
            else:
                logger.error(f"Failed to send reply for email {email_data['uid']}")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Failed to send reply email"
                )
                return False
                
        except Exception as e:
            logger.error(f"Failed to process email {email_data.get('uid', 'unknown')}: {e}")
            return False

    def _format_html_response(self, response_content: str, pending_emails: List[Any]) -> str:
        """Format the agent response and pending emails as HTML.

        Args:
            response_content: Main response from the agent
            pending_emails: List of EmailMessage objects

        Returns:
            HTML formatted email content
        """
        # Convert plain text response to HTML (preserve line breaks)
        html_response = response_content.replace('\n', '<br>\n')

        # Start building HTML email
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }}
        .main-response {{
            background-color: #f9f9f9;
            padding: 20px;
            border-left: 4px solid #007bff;
            margin-bottom: 30px;
        }}
        .pending-emails {{
            border-top: 2px solid #dee2e6;
            padding-top: 20px;
        }}
        .pending-emails h2 {{
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }}
        .email-item {{
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .email-header {{
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 15px;
        }}
        .email-field {{
            margin-bottom: 5px;
        }}
        .email-field strong {{
            color: #495057;
            min-width: 80px;
            display: inline-block;
        }}
        .email-body {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #28a745;
        }}
        .separator {{
            text-align: center;
            margin: 30px 0;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="main-response">
        {html_response}
    </div>"""

        # Add pending emails section if there are any
        if pending_emails:
            html_content += """
    <div class="separator">
        <strong>═══════════════════════════════════════════════════════════</strong>
    </div>

    <div class="pending-emails">
        <h2>📧 E-Mails zum Versenden</h2>
        <p><em>Bitte kopieren Sie die gewünschten E-Mails und senden Sie sie manuell:</em></p>
"""

            for i, email in enumerate(pending_emails, 1):
                # Escape HTML in email content
                to_email = self._escape_html(email.to_email)
                subject = self._escape_html(email.subject)
                body = self._escape_html(email.body)
                from_name = self._escape_html(email.from_name or 'Standard-Absender')

                html_content += f"""
        <div class="email-item">
            <div class="email-header">
                <h3>E-Mail #{i}</h3>
                <div class="email-field"><strong>An:</strong> {to_email}</div>
                <div class="email-field"><strong>Betreff:</strong> {subject}</div>
                <div class="email-field"><strong>Von:</strong> {from_name}</div>
            </div>
            <div class="email-body">{body}</div>
        </div>"""

            html_content += """
    </div>"""

        html_content += """
</body>
</html>"""

        return html_content

    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters.

        Args:
            text: Text to escape

        Returns:
            HTML-escaped text
        """
        if not text:
            return ""

        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;')
                   .replace('\n', '<br>\n'))

    def get_whitelist_status(self) -> Dict[str, Any]:
        """Get current whitelist configuration and status.

        Returns:
            Dictionary with whitelist status information
        """
        whitelist_info = self.whitelist.get_whitelist_info()

        # Get statistics about whitelist rejections from database
        try:
            whitelist_stats = self.database.get_whitelist_rejection_stats()
        except Exception as e:
            logger.warning(f"Could not retrieve whitelist statistics: {e}")
            whitelist_stats = {
                "total_rejected": 0,
                "recent_rejections": []
            }

        return {
            "whitelist_enabled": whitelist_info["enabled"],
            "whitelist_entries": {
                "exact_emails": whitelist_info["exact_emails"],
                "domain_patterns": whitelist_info["domain_patterns"],
                "total_count": whitelist_info["total_entries"]
            },
            "rejection_stats": whitelist_stats
        }



    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status and statistics.
        
        Returns:
            Status information dictionary
        """
        try:
            stats = self.database.get_processing_stats()
            
            # Add connection status
            imap_connected = self.email_connection.connect_imap()
            if imap_connected:
                self.email_connection.disconnect()
            
            smtp_connected = self.email_connection.connect_smtp()
            if smtp_connected:
                self.email_connection.disconnect()
            
            # Add whitelist status
            whitelist_status = self.get_whitelist_status()

            stats.update({
                "imap_connection": imap_connected,
                "smtp_connection": smtp_connected,
                "status": "healthy" if (imap_connected and smtp_connected) else "degraded",
                "whitelist": whitelist_status
            })

            return stats
            
        except Exception as e:
            logger.error(f"Failed to get processing status: {e}")
            # Try to get whitelist status even in error case
            try:
                whitelist_status = self.get_whitelist_status()
            except Exception:
                whitelist_status = {
                    "whitelist_enabled": False,
                    "whitelist_entries": {"total_count": 0},
                    "rejection_stats": {"total_rejected": 0}
                }

            return {
                "status": "error",
                "error": str(e),
                "total_processed": 0,
                "successful_responses": 0,
                "failed_processing": 0,
                "last_sync": None,
                "success_rate": 0,
                "imap_connection": False,
                "smtp_connection": False,
                "whitelist": whitelist_status
            }
